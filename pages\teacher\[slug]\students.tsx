// pages/teacher/[slug]/students.tsx - Server Component
import { useParams } from 'blade/hooks';
import { use, useBatch } from 'blade/server/hooks';
import StudentManagementTabs from '../../../components/auth/teacher/students/student-management-tabs.client';

const TeachersStudentsPage = () => {
  const { slug } = useParams();

  // Use useBatch to optimize queries and prevent re-render issues
  const [teacher, students] = useBatch(() => {
    const teacherQuery = use.users({
      with: { slug: slug, role: 'teacher' }
    });

    if (!teacherQuery) {
      return [null, []];
    }

    const studentsQuery = use.users({
      with: { teacherId: teacherQuery.id, role: 'student' },
      orderedBy: { ascending: ['name'] }
    });

    return [teacherQuery, studentsQuery || []];
  });

  if (!teacher) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold text-red-600">Teacher not found</h1>
      </div>
    );
  }

  // Fetch teacher's classes (if you have a classes model)
  // const classes = use.classes({
  //   with: { teacherId: teacher.id },
  //   orderedBy: { ascending: ['name'] }
  // });

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-4">Student Management</h1>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Manage your students and their class assignments. Total students: {students?.length || 0}
        </p>
      </div>

      {/* Pass server data to client component */}
      <StudentManagementTabs
        students={students}
        teacher={teacher}
        // classes={classes || []}
      />
    </div>
  );
};

// Export as default for Blade framework
export default TeachersStudentsPage;
