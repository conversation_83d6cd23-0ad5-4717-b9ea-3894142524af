// triggers/send-student-invitation.ts
import { resend, validateEmailConfig } from '../lib/resend';
import { StudentInvitationEmail } from '../components/emails/student-invitation';
import { render } from '@react-email/render';

// This trigger will be called after a student user is created
export const afterAdd = async (query: any, multiple: boolean, options: any) => {
  // Only process single user creation (not bulk operations)
  if (multiple) return [];

  // Only process student users
  if (query.with?.role !== 'student') return [];

  try {
    // Validate email configuration
    const emailValidation = validateEmailConfig();
    if (!emailValidation.isValid) {
      console.error('Email configuration error:', emailValidation.error);
      return []; // Don't fail the user creation if email fails
    }

    const studentData = query.with;

    // For now, we'll need to get teacher info differently since we don't have access to ronin here
    // This is a limitation - we might need to pass teacher info through the mutation
    // or use a different approach

    console.log('Student created, attempting to send invitation email:', {
      name: studentData.name,
      email: studentData.email,
      teacherId: studentData.teacherId
    });

    // For now, we'll skip the email sending in the trigger
    // and handle it differently since we need teacher information
    // TODO: Implement email sending when we have proper access to teacher data

    console.log('Student invitation trigger completed - email sending skipped for now');

  } catch (error) {
    console.error('Error in student invitation trigger:', error);
    // Don't throw - we don't want to fail the user creation if email fails
  }

  // Return empty array since we don't need additional queries
  return [];
};
