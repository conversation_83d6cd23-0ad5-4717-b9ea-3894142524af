// router.ts
import { Hono } from "hono";
import { cors } from "hono/cors";
import { ronin, type Bindings, type Variables } from "blade-hono";
import { resend } from "./lib/resend";
import { WaitlistEmail as TeacherWaitlistEmail } from "./components/waitlist/teacher";
import { WaitlistEmail as SchoolWaitlistEmail } from "./components/waitlist/school";


// Import the unified auth instance
import { auth } from "./lib/auth";

// Define the Hono app with proper typing for RONIN and auth context
interface AppContext extends Variables {
  ronin: any;
  user: typeof auth.$Infer.Session.user | null;
  session: typeof auth.$Infer.Session.session | null;
}

const app = new Hono<{
  Bindings: Bindings;
  Variables: AppContext;
}>();

// Get environment variables - Blade 0.9.3+ handles server/client context automatically
const RONIN_TOKEN = process.env["BLADE_RONIN_TOKEN"] || process.env["RONIN_TOKEN"] || '';
const CORS_ORIGIN = process.env["BLADE_BETTER_AUTH_URL"] || 'http://localhost:3000';
const GOOGLE_PLACES_API_KEY = process.env["BLADE_GOOGLE_PLACES_API_KEY"] || '';

// Add RONIN middleware with explicit token since Blade doesn't set up c.env properly
if (RONIN_TOKEN) {
  app.use("*", ronin({
    token: RONIN_TOKEN
  }));
} else {
  console.error('RONIN_TOKEN not found in environment variables');
}

// Configure CORS specifically for auth routes (Better Auth needs this)
app.use(
  "/api/auth/*",
  cors({
    origin: CORS_ORIGIN,
    allowHeaders: ["Content-Type", "Authorization"],
    allowMethods: ["POST", "GET", "OPTIONS"],
    exposeHeaders: ["Content-Length"],
    maxAge: 600,
    credentials: true,
  })
);

// Configure CORS for other routes
app.use(
  "*",
  cors({
    origin: CORS_ORIGIN,
    allowHeaders: ["Content-Type", "Authorization"],
    allowMethods: ["POST", "GET", "OPTIONS"],
    exposeHeaders: ["Content-Length"],
    maxAge: 600,
    credentials: true,
  })
);

// Add Better Auth session middleware (skip for auth endpoints to avoid circular calls)
app.use("*", async (c, next) => {
  // Skip session middleware for auth endpoints to avoid circular dependency
  if (c.req.path.startsWith('/api/auth/')) {
    return next();
  }
  
  // For now, set null values to avoid the headers error
  // The client will handle session management directly
  c.set("user", null);
  c.set("session", null);
  
  return next();
});

// Mount Better Auth API endpoints - single unified endpoint
app.on(["POST", "GET"], "/api/auth/*", async (c) => {
  const path = c.req.path;
  const method = c.req.method;
  
  console.log(`Better Auth request: ${method} ${path}`);
  
  // Handle waitlist validation BEFORE calling Better Auth for OTP sending
  if (path === "/api/auth/email-otp/send-verification-otp" && method === "POST") {
    try {
      // Clone the request to avoid locking the ReadableStream
      const clonedRequest = c.req.raw.clone();
      const body = await clonedRequest.json();
      const email = body?.email;

      if (email) {
        console.log(`Checking waitlist for email: ${email}`);

        // Get RONIN instance using the correct blade-hono syntax
        const ronin = c.var.ronin;

        // Check if user exists in waitlist
        const waitlistUser = await ronin.get.waitlist({
          with: { email: email }
        });

        if (!waitlistUser) {
          console.error(`User ${email} not found in waitlist - blocking OTP send`);
          return c.json({
            error: {
              message: "Your email is not on the waitlist. Please sign up first.",
              code: "WAITLIST_REQUIRED"
            }
          }, 400);
        }

        // Check if the user has been approved
        if (!waitlistUser.isApproved) {
          console.error(`User ${email} not approved in waitlist - blocking OTP send`);
          return c.json({
            error: {
              message: "Your account is awaiting approval. Please check back later.",
              code: "WAITLIST_NOT_APPROVED"
            }
          }, 400);
        }

        // NEW: Check if the user's role matches the context they're trying to sign in from
        // Get the selected role from the request header
        const selectedRole = c.req.header('X-Selected-Role') || 'teacher'; // default to teacher
        const expectedUserType = selectedRole; // Use the role selected in the UI

        console.log(`User attempting to sign in as: ${expectedUserType}, waitlist shows: ${waitlistUser.userType}`);

        // Check if waitlist userType matches expected role
        if (waitlistUser.userType !== expectedUserType) {
          const correctRole = waitlistUser.userType === 'school_admin' ? 'School Admin' : 'Teacher';

          console.error(`User ${email} is registered as ${waitlistUser.userType} but trying to sign in as ${expectedUserType}`);
          return c.json({
            error: {
              message: `This email is registered for ${correctRole} access.`,
              code: "WRONG_ROLE_SELECTED"
            }
          }, 400);
        }

        console.log(`User ${email} is approved in waitlist with correct role (${waitlistUser.userType}) - allowing OTP send`);
      }
    } catch (error) {
      console.error('Error checking waitlist before OTP send:', error);
      return c.json({
        error: {
          message: "Unable to verify waitlist status. Please try again later.",
          code: "WAITLIST_VALIDATION_ERROR"
        }
      }, 500);
    }
  }
  
  try {
    // Create a wrapped request that handles potential fetch issues
    const wrappedRequest = new Request(c.req.raw.url, {
      method: c.req.raw.method,
      headers: c.req.raw.headers,
      body: c.req.raw.body,
    });
    
    const response = await auth.handler(wrappedRequest);
    
    // Ensure we have a proper Response object
    if (!response) {
      console.error("Better Auth handler returned undefined response for:", path);
      return c.json({
        error: {
          message: "Authentication service error",
          code: "AUTH_SERVICE_ERROR"
        }
      }, 500);
    }

    // Check if response has proper structure before accessing headers
    if (typeof response !== 'object' || !('headers' in response)) {
      console.error("Better Auth response missing headers for:", path);
      return c.json({
        error: {
          message: "Authentication service error",
          code: "AUTH_SERVICE_ERROR"
        }
      }, 500);
    }

    // Log response details for debugging
    console.log(`Better Auth response status: ${response.status} for ${path}`);
    
    // Return the response directly - let Better Auth handle the formatting
    return response;
    
  } catch (error) {
    console.error(`Error in Better Auth handler for ${path}:`, error);
    
    // Handle specific waitlist validation errors first
    if (error instanceof Error) {
      if (error.message === 'WAITLIST_REQUIRED') {
        return c.json({
          error: {
            message: "Your email is not on the waitlist. Please sign up first.",
            code: "WAITLIST_REQUIRED"
          }
        }, 400);
      } else if (error.message === 'WAITLIST_NOT_APPROVED') {
        return c.json({
          error: {
            message: "Your account is awaiting approval. Please check back later.",
            code: "WAITLIST_NOT_APPROVED"
          }
        }, 400);
      } else if (error.message === 'WAITLIST_VALIDATION_ERROR') {
        return c.json({
          error: {
            message: "Unable to verify waitlist status. Please try again later.",
            code: "WAITLIST_VALIDATION_ERROR"
          }
        }, 500);
      } else if (error.message.includes('Email service')) {
        return c.json({
          error: {
            message: "Email service is temporarily unavailable. Please try again later.",
            code: "EMAIL_SERVICE_ERROR"
          }
        }, 500);
      } else if (error.message.includes('result.headers') || error.message.includes('undefined is not an object')) {
        // Handle the specific Better Auth headers error
        console.error('Better Auth headers error detected, likely after successful operation');
        // For OTP send operations, if we get here it might mean the OTP was sent but there's a response formatting issue
        if (path.includes('send-verification-otp')) {
          return c.json({
            error: {
              message: "OTP service temporarily unavailable. Please try again.",
              code: "OTP_SERVICE_ERROR"
            }
          }, 500);
        }
      }
    }
    
    return c.json({
      error: {
        message: "Authentication service error",
        code: "AUTH_SERVICE_ERROR",
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    }, 500);
  }
});

// School search endpoint using Google Places API
app.post("/api/search-schools", async (c) => {
  console.log('School search endpoint called');
  
  try {
    const body = await c.req.json();
    console.log('Request body:', body);
    
    const { query } = body;

    if (!query || query.length < 3) {
      console.log('Query too short or missing:', query);
      return c.json({ schools: [] }, 200);
    }

    if (!GOOGLE_PLACES_API_KEY) {
      console.error("Google Places API key not configured");
      return c.json({ error: "School search service unavailable" }, 500);
    }

    console.log('Making request to Google Places API with query:', query);

    // Use Google Places API Text Search to find schools
    const searchUrl = new URL('https://maps.googleapis.com/maps/api/place/textsearch/json');
    searchUrl.searchParams.append('query', `${query} school`);
    searchUrl.searchParams.append('type', 'school');
    searchUrl.searchParams.append('key', GOOGLE_PLACES_API_KEY);

    console.log('Google Places URL:', searchUrl.toString().replace(GOOGLE_PLACES_API_KEY, 'BLADE_GOOGLE_PLACES_API_KEY'));

    const response = await fetch(searchUrl.toString());
    console.log('Google Places response status:', response.status);

    if (!response.ok) {
      console.error('Google Places API HTTP error:', response.status, response.statusText);
      const errorText = await response.text();
      console.error('Google Places error response:', errorText);
      return c.json({ error: "Failed to search schools", details: "API request failed" }, 500);
    }

    const data = await response.json();
    console.log('Google Places response status field:', data.status);
    console.log('Google Places results count:', data.results?.length || 0);

    if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
      console.error('Google Places API error:', data.status, data.error_message);
      return c.json({ 
        error: "Failed to search schools", 
        details: data.error_message || data.status 
      }, 500);
    }

    // Transform the results to our format
    const schools = data.results?.slice(0, 10).map((place: any) => ({
      place_id: place.place_id,
      name: place.name,
      formatted_address: place.formatted_address,
    })) || [];

    console.log('Returning schools:', schools.length);

    // Make sure we return a clean JSON response
    const responseData = { schools };
    
    // Set proper headers to ensure clean JSON response
    c.header('Content-Type', 'application/json');
    return c.json(responseData, 200);

  } catch (error) {
    console.error("School search error:", error);
    
    // Return a proper JSON error response with clean headers
    c.header('Content-Type', 'application/json');
    return c.json({ 
      error: "School search failed",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

// Get registered schools endpoint
app.get("/api/registered-schools", async (c) => {
  try {
    // Get RONIN instance from middleware
    const ronin = c.var.ronin;

    if (!ronin) {
      console.error('RONIN instance not available');
      c.header('Content-Type', 'application/json');
      return c.json({
        error: "Database service unavailable",
        registeredPlaceIds: [] // Return empty array as fallback
      }, 500);
    }

    // Get all school admins from waitlist to check which schools are already registered
    let registeredSchools = [];
    try {
      // Query for school admins specifically
      const schoolAdminQuery = await ronin.get.waitlist({
        with: {
          userType: 'school_admin'
        }
      });

      // Handle the case where RONIN returns a single object vs array
      if (schoolAdminQuery) {
        registeredSchools = Array.isArray(schoolAdminQuery) ? schoolAdminQuery : [schoolAdminQuery];
      } else {
        // No school admins found, which is fine
        registeredSchools = [];
      }
    } catch (queryError) {
      console.error('Query error:', queryError);
      registeredSchools = [];
    }

    // Extract place IDs of registered schools
    const registeredPlaceIds = registeredSchools
      .filter((entry: any) => entry && entry.schoolPlaceId)
      .map((entry: any) => entry.schoolPlaceId);

    c.header('Content-Type', 'application/json');
    return c.json({ registeredPlaceIds }, 200);

  } catch (error) {
    console.error("Error fetching registered schools:", error);
    c.header('Content-Type', 'application/json');
    return c.json({
      error: "Failed to fetch registered schools",
      details: error instanceof Error ? error.message : 'Unknown error',
      registeredPlaceIds: [] // Return empty array as fallback
    }, 200); // Return 200 with empty array instead of 500 error
  }
});

// Enhanced waitlist signup endpoint with school support
app.post("/api/join-waitlist", async (c) => {
  try {
    const { email, userType, name, school } = await c.req.json();

    if (!email || !userType) {
      c.header('Content-Type', 'application/json');
      return c.json({ error: "Email and user type are required" }, 400);
    }

    // Validate user type
    if (!['teacher', 'school_admin'].includes(userType)) {
      c.header('Content-Type', 'application/json');
      return c.json({ error: "Invalid user type" }, 400);
    }

    // For school admins, require school information
    if (userType === 'school_admin' && !school) {
      c.header('Content-Type', 'application/json');
      return c.json({ error: "School information is required for school administrators" }, 400);
    }

    // Get RONIN instance using the correct blade-hono syntax
    const ronin = c.var.ronin;
    
    // Check if email already exists in waitlist (regardless of userType)
    try {
      const existingEmailEntry = await ronin.get.waitlist({
        with: {
          email: email
        }
      });

      if (existingEmailEntry) {
        c.header('Content-Type', 'application/json');
        return c.json({ error: "Email already exists" }, 400);
      }
    } catch (error) {
      console.error('Error checking existing email:', error);
      // Continue with the signup process - the unique constraint will catch duplicates
    }

    // For school admins, also check if the school is already registered
    if (userType === 'school_admin' && school) {
      try {
        const existingSchoolEntry = await ronin.get.waitlist({
          with: {
            schoolPlaceId: school.place_id,
            userType: 'school_admin'
          }
        });

        // Handle both single object and array responses from RONIN
        if (existingSchoolEntry) {
          const schoolEntries = Array.isArray(existingSchoolEntry) ? existingSchoolEntry : [existingSchoolEntry];
          if (schoolEntries.length > 0) {
            c.header('Content-Type', 'application/json');
            return c.json({ error: "School already registered" }, 400);
          }
        }
      } catch (error) {
        console.error('Error checking existing school:', error);
        // Continue with the signup process - the unique constraint will catch duplicates
      }
    }

    // Prepare waitlist data
    const waitlistData: any = {
      email,
      userType: userType,
      createdAt: new Date(),
    };

    // Add school information for school admins
    if (userType === 'school_admin' && school) {
      waitlistData.schoolPlaceId = school.place_id;
      waitlistData.schoolName = school.name;
      waitlistData.schoolAddress = school.formatted_address;
    }

    // Add to waitlist database
    try {
      await ronin.add.waitlist.with(waitlistData);
    } catch (error) {
      console.error('Error adding to waitlist:', error);
      // Check if it's a unique constraint violation
      if (error instanceof Error && (
        error.message.includes('unique') ||
        error.message.includes('duplicate') ||
        error.message.includes('UNIQUE constraint failed')
      )) {
        c.header('Content-Type', 'application/json');
        return c.json({ error: "Email already exists" }, 400);
      }
      // Re-throw other errors
      throw error;
    }

    // Choose the appropriate email template
    const EmailTemplate = userType === 'teacher' ? TeacherWaitlistEmail : SchoolWaitlistEmail;

    // Send confirmation email
    const emailResult = await resend.emails.send({
      from: "Penned <<EMAIL>>",
      to: email,
      subject: `Welcome to the Penned ${userType === 'teacher' ? 'Teacher' : 'School'} Waitlist`,
      react: EmailTemplate({ 
        name: name || email.split('@')[0],
        school: userType === 'school_admin' ? school : undefined
      }) as any,
    });

    if (emailResult.error) {
      console.error("Error sending email:", emailResult.error);
      c.header('Content-Type', 'application/json');
      return c.json({ error: "Failed to send confirmation email" }, 500);
    }

    c.header('Content-Type', 'application/json');
    return c.json({
      message: "Successfully joined waitlist",
      email: email,
      userType: userType
    }, 200);

  } catch (error) {
    console.error("Waitlist signup error:", error);
    
    c.header('Content-Type', 'application/json');
    return c.json({
      error: "Waitlist signup failed",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

// Update user endpoint for post-OTP user updates
app.post("/api/update-user", async (c) => {
  try {
    const { userId, updates } = await c.req.json();

    if (!userId || !updates) {
      c.header('Content-Type', 'application/json');
      return c.json({ error: "Missing userId or updates" }, 400);
    }

    console.log('Updating user:', userId, 'with:', updates);

    // Get RONIN instance
    const ronin = c.var.ronin;

    if (!ronin) {
      console.error('RONIN instance not available');
      c.header('Content-Type', 'application/json');
      return c.json({ error: "Database service unavailable" }, 500);
    }

    // Update user in database
    await ronin.set.user({
      with: { id: userId },
      to: updates
    });

    console.log('User updated successfully:', userId);

    c.header('Content-Type', 'application/json');
    return c.json({ success: true });

  } catch (error) {
    console.error('Error updating user:', error);
    c.header('Content-Type', 'application/json');
    return c.json({ error: "Failed to update user" }, 500);
  }
})





export default app;

