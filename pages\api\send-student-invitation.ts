// pages/api/send-student-invitation.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import { resend, validateEmailConfig } from '../../lib/resend';
import { StudentInvitationEmail } from '../../components/emails/student-invitation';
import { render } from '@react-email/render';

interface StudentInvitationRequest {
  studentName: string;
  teacherName: string;
  username: string;
  email: string;
  classes: string[];
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Validate email configuration
    const emailValidation = validateEmailConfig();
    if (!emailValidation.isValid) {
      console.error('Email configuration error:', emailValidation.error);
      return res.status(500).json({ 
        error: 'Email service not configured',
        details: emailValidation.error 
      });
    }

    const { studentName, teacherN<PERSON>, username, email, classes }: StudentInvitationRequest = req.body;

    // Validate required fields
    if (!studentName || !teacherName || !username || !email) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        required: ['studentName', 'teacherName', 'username', 'email']
      });
    }

    // Generate the login URL
    const baseUrl = process.env.BLADE_BETTER_AUTH_URL || 'http://localhost:3000';
    const loginUrl = `${baseUrl}/login?role=student`;

    // Render the email template
    const emailHtml = render(
      StudentInvitationEmail({
        studentName,
        teacherName,
        username,
        email,
        classes: classes || [],
        loginUrl
      })
    );

    // Send the email
    const result = await resend.emails.send({
      from: 'Penned <<EMAIL>>',
      to: email,
      subject: `Welcome to Penned - Your Student Account is Ready!`,
      html: emailHtml,
    });

    if (result.error) {
      console.error('Resend API error:', result.error);
      return res.status(500).json({ 
        error: 'Failed to send email',
        details: result.error.message || 'Unknown error'
      });
    }

    console.log('Student invitation email sent successfully to:', email);
    return res.status(200).json({ 
      success: true, 
      messageId: result.data?.id,
      message: 'Invitation email sent successfully'
    });

  } catch (error) {
    console.error('Error sending student invitation email:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
